'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  Send,
  Facebook,
  Instagram,
  Linkedin,
  Github 
} from 'lucide-react';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';

export const metadata = {
  title: "Contatti - ProgrammArti | Richiedi Preventivo Gratuito",
  description: "Contatta ProgrammArti per il tuo progetto digitale. Richiedi un preventivo gratuito per siti web, e-commerce, sistemi gestionali e servizi NFC.",
  keywords: "contatti, preventivo gratuito, agenzia web Roma, sviluppo web, consulenza digitale",
};

const ContactPage = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const { register, handleSubmit, formState: { errors }, reset } = useForm();

  const contactInfo = [
    {
      icon: Mail,
      title: "Email",
      value: "<EMAIL>",
      link: "mailto:<EMAIL>"
    },
    {
      icon: Phone,
      title: "Telefono",
      value: "+39 ************",
      link: "tel:+393123456789"
    },
    {
      icon: MapPin,
      title: "Indirizzo",
      value: "Via Roma 123, 00100 Roma, Italia",
      link: "https://maps.google.com/?q=Via+Roma+123+Roma"
    },
    {
      icon: Clock,
      title: "Orari",
      value: "Lun-Ven: 9:00-18:00",
      link: null
    }
  ];

  const socialLinks = [
    { name: 'Facebook', icon: Facebook, href: '#', color: 'hover:text-blue-600' },
    { name: 'Instagram', icon: Instagram, href: '#', color: 'hover:text-pink-600' },
    { name: 'LinkedIn', icon: Linkedin, href: '#', color: 'hover:text-blue-700' },
    { name: 'GitHub', icon: Github, href: '#', color: 'hover:text-gray-800' },
  ];

  const services = [
    "Sviluppo Siti Web",
    "E-commerce",
    "Sistemi Gestionali",
    "Web Marketing",
    "Servizi NFC",
    "Consulenza Digitale",
    "Altro"
  ];

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      // Simulate form submission
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('Form data:', data);
      setSubmitStatus('success');
      reset();
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-background via-background-secondary to-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-text-primary mb-6">
              Contatta{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent">
                ProgrammArti
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
              Hai un progetto in mente? Contattaci per una consulenza gratuita 
              e scopri come possiamo trasformare le tue idee in realtà digitale.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Card className="h-full">
                <h2 className="text-2xl font-bold text-text-primary mb-6">
                  Richiedi un Preventivo Gratuito
                </h2>
                
                {submitStatus === 'success' && (
                  <div className="mb-6 p-4 bg-green-100 border border-green-300 rounded-lg">
                    <p className="text-green-800">
                      Grazie per il tuo messaggio! Ti risponderemo entro 24 ore.
                    </p>
                  </div>
                )}
                
                {submitStatus === 'error' && (
                  <div className="mb-6 p-4 bg-red-100 border border-red-300 rounded-lg">
                    <p className="text-red-800">
                      Si è verificato un errore. Riprova più tardi o contattaci direttamente.
                    </p>
                  </div>
                )}

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">
                        Nome *
                      </label>
                      <input
                        type="text"
                        {...register('firstName', { required: 'Il nome è obbligatorio' })}
                        className="w-full px-4 py-3 bg-input-background border border-input-border rounded-lg text-input-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                        placeholder="Il tuo nome"
                      />
                      {errors.firstName && (
                        <p className="mt-1 text-sm text-red-600">{errors.firstName.message}</p>
                      )}
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">
                        Cognome *
                      </label>
                      <input
                        type="text"
                        {...register('lastName', { required: 'Il cognome è obbligatorio' })}
                        className="w-full px-4 py-3 bg-input-background border border-input-border rounded-lg text-input-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                        placeholder="Il tuo cognome"
                      />
                      {errors.lastName && (
                        <p className="mt-1 text-sm text-red-600">{errors.lastName.message}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Email *
                    </label>
                    <input
                      type="email"
                      {...register('email', { 
                        required: 'L\'email è obbligatoria',
                        pattern: {
                          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                          message: 'Inserisci un\'email valida'
                        }
                      })}
                      className="w-full px-4 py-3 bg-input-background border border-input-border rounded-lg text-input-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                      placeholder="<EMAIL>"
                    />
                    {errors.email && (
                      <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Telefono
                    </label>
                    <input
                      type="tel"
                      {...register('phone')}
                      className="w-full px-4 py-3 bg-input-background border border-input-border rounded-lg text-input-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                      placeholder="+39 ************"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Servizio di Interesse
                    </label>
                    <select
                      {...register('service')}
                      className="w-full px-4 py-3 bg-input-background border border-input-border rounded-lg text-input-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                    >
                      <option value="">Seleziona un servizio</option>
                      {services.map((service) => (
                        <option key={service} value={service}>
                          {service}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Messaggio *
                    </label>
                    <textarea
                      {...register('message', { required: 'Il messaggio è obbligatorio' })}
                      rows={5}
                      className="w-full px-4 py-3 bg-input-background border border-input-border rounded-lg text-input-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200 resize-none"
                      placeholder="Descrivi il tuo progetto o le tue esigenze..."
                    />
                    {errors.message && (
                      <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
                    )}
                  </div>

                  <div className="flex items-start">
                    <input
                      type="checkbox"
                      {...register('privacy', { required: 'Devi accettare la privacy policy' })}
                      className="mt-1 mr-3"
                    />
                    <label className="text-sm text-text-secondary">
                      Accetto la{' '}
                      <a href="/privacy" className="text-primary hover:underline">
                        Privacy Policy
                      </a>{' '}
                      e autorizzo il trattamento dei miei dati personali. *
                    </label>
                  </div>
                  {errors.privacy && (
                    <p className="text-sm text-red-600">{errors.privacy.message}</p>
                  )}

                  <Button
                    type="submit"
                    size="lg"
                    className="w-full"
                    loading={isSubmitting}
                  >
                    <Send className="w-5 h-5 mr-2" />
                    {isSubmitting ? 'Invio in corso...' : 'Invia Messaggio'}
                  </Button>
                </form>
              </Card>
            </motion.div>

            {/* Contact Info */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="space-y-8"
            >
              <div>
                <h2 className="text-2xl font-bold text-text-primary mb-6">
                  Informazioni di Contatto
                </h2>
                <div className="space-y-6">
                  {contactInfo.map((info) => {
                    const Icon = info.icon;
                    const content = (
                      <div className="flex items-start space-x-4">
                        <div className="p-3 bg-primary/10 rounded-lg">
                          <Icon className="w-6 h-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-text-primary mb-1">
                            {info.title}
                          </h3>
                          <p className="text-text-secondary">
                            {info.value}
                          </p>
                        </div>
                      </div>
                    );

                    return info.link ? (
                      <a
                        key={info.title}
                        href={info.link}
                        className="block hover:bg-background-secondary p-4 rounded-lg transition-colors duration-200"
                        target={info.link.startsWith('http') ? '_blank' : undefined}
                        rel={info.link.startsWith('http') ? 'noopener noreferrer' : undefined}
                      >
                        {content}
                      </a>
                    ) : (
                      <div key={info.title} className="p-4">
                        {content}
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Social Links */}
              <div>
                <h3 className="text-lg font-semibold text-text-primary mb-4">
                  Seguici sui Social
                </h3>
                <div className="flex space-x-4">
                  {socialLinks.map((social) => {
                    const Icon = social.icon;
                    return (
                      <a
                        key={social.name}
                        href={social.href}
                        className={`p-3 bg-background-secondary rounded-lg text-text-secondary transition-colors duration-200 ${social.color}`}
                        aria-label={social.name}
                      >
                        <Icon className="w-6 h-6" />
                      </a>
                    );
                  })}
                </div>
              </div>

              {/* Map Placeholder */}
              <Card>
                <h3 className="text-lg font-semibold text-text-primary mb-4">
                  La Nostra Sede
                </h3>
                <div className="aspect-video bg-background-secondary rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <MapPin className="w-12 h-12 text-primary mx-auto mb-2" />
                    <p className="text-text-secondary">
                      Mappa interattiva<br />
                      Via Roma 123, Roma
                    </p>
                  </div>
                </div>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;
