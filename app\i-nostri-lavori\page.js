'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { ExternalLink, Github, Filter } from 'lucide-react';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import Modal from '../components/ui/Modal';

export const metadata = {
  title: "I Nostri Lavori - Portfolio ProgrammArti | Progetti Web, E-commerce, Gestionali",
  description: "Scopri il portfolio di ProgrammArti: siti web, e-commerce, sistemi gestionali e campagne di marketing digitale realizzati per i nostri clienti.",
  keywords: "portfolio, progetti web, e-commerce, sistemi gestionali, siti web, lavori realizzati",
};

const PortfolioPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedProject, setSelectedProject] = useState(null);

  const categories = ['All', 'Website', 'E-commerce', 'Management System', 'Marketing'];

  const projects = [
    {
      id: 1,
      title: "E-commerce Fashion Store",
      description: "Piattaforma e-commerce completa per un brand di moda con sistema di gestione inventario, pagamenti integrati e dashboard analytics avanzata.",
      fullDescription: "Questo progetto ha richiesto lo sviluppo di una piattaforma e-commerce completa con funzionalità avanzate di gestione inventario, sistema di pagamenti multipli, dashboard analytics in tempo reale e integrazione con sistemi di spedizione. Il sito è stato ottimizzato per le conversioni e include funzionalità di wishlist, recensioni prodotti e sistema di raccomandazioni personalizzate.",
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop&crop=center",
      tags: ["React", "Node.js", "Stripe", "MongoDB"],
      category: "E-commerce",
      liveUrl: "#",
      githubUrl: "#",
      client: "Fashion Brand XYZ",
      duration: "3 mesi",
      features: ["Gestione inventario", "Pagamenti multipli", "Dashboard analytics", "Sistema recensioni"]
    },
    {
      id: 2,
      title: "Sistema Gestionale Ristorante",
      description: "Applicazione web per la gestione completa di un ristorante: ordini, menu, prenotazioni, inventario e reportistica avanzata.",
      fullDescription: "Sistema gestionale completo sviluppato per una catena di ristoranti che include gestione ordini in tempo reale, sistema di prenotazioni online, gestione menu dinamica, controllo inventario automatizzato e reportistica avanzata con analytics predittive per ottimizzare le operazioni.",
      image: "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=600&h=400&fit=crop&crop=center",
      tags: ["Vue.js", "Laravel", "MySQL", "Socket.io"],
      category: "Management System",
      liveUrl: "#",
      githubUrl: "#",
      client: "Ristoranti del Centro",
      duration: "4 mesi",
      features: ["Gestione ordini", "Prenotazioni online", "Controllo inventario", "Reportistica avanzata"]
    },
    {
      id: 3,
      title: "Portfolio Architetto",
      description: "Sito web portfolio per studio di architettura con galleria interattiva, sistema CMS personalizzato e ottimizzazione SEO.",
      fullDescription: "Sito web portfolio elegante e moderno per uno studio di architettura, featuring una galleria interattiva con filtri avanzati, sistema CMS personalizzato per la gestione dei progetti, ottimizzazione SEO completa e integrazione con social media per massimizzare la visibilità online.",
      image: "https://images.unsplash.com/photo-1503387762-592deb58ef4e?w=600&h=400&fit=crop&crop=center",
      tags: ["Next.js", "Sanity CMS", "SEO", "Framer Motion"],
      category: "Website",
      liveUrl: "#",
      githubUrl: "#",
      client: "Studio Architettura Moderna",
      duration: "2 mesi",
      features: ["Galleria interattiva", "CMS personalizzato", "SEO ottimizzato", "Design responsive"]
    },
    {
      id: 4,
      title: "Campagna Marketing Digitale",
      description: "Strategia di marketing digitale completa per azienda B2B con focus su lead generation e conversioni.",
      fullDescription: "Campagna di marketing digitale integrata che ha incluso strategia SEO/SEM, social media marketing, email marketing automation e content marketing. La campagna ha generato un aumento del 300% dei lead qualificati e un ROI del 450% in 6 mesi.",
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=400&fit=crop&crop=center",
      tags: ["Google Ads", "Facebook Ads", "SEO", "Email Marketing"],
      category: "Marketing",
      liveUrl: "#",
      githubUrl: "#",
      client: "TechSolutions B2B",
      duration: "6 mesi",
      features: ["Lead generation", "Marketing automation", "Analytics avanzati", "ROI tracking"]
    },
    {
      id: 5,
      title: "App Mobile E-learning",
      description: "Piattaforma e-learning mobile-first con sistema di gamification e tracking progressi studenti.",
      fullDescription: "Piattaforma e-learning innovativa con approccio mobile-first, sistema di gamification per aumentare l'engagement, tracking dettagliato dei progressi, quiz interattivi e sistema di certificazioni. Include anche dashboard per insegnanti e analytics avanzati.",
      image: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop&crop=center",
      tags: ["React Native", "Firebase", "Gamification", "Analytics"],
      category: "Website",
      liveUrl: "#",
      githubUrl: "#",
      client: "EduTech Academy",
      duration: "5 mesi",
      features: ["Mobile-first", "Gamification", "Tracking progressi", "Sistema certificazioni"]
    },
    {
      id: 6,
      title: "Marketplace B2B",
      description: "Marketplace B2B per connettere fornitori e acquirenti con sistema di rating e gestione ordini complessa.",
      fullDescription: "Marketplace B2B complesso che connette fornitori e acquirenti industriali. Include sistema di rating e recensioni, gestione ordini multi-step, sistema di pagamenti escrow, chat integrata e dashboard analytics per monitorare le performance delle transazioni.",
      image: "https://images.unsplash.com/photo-1556761175-b413da4baf72?w=600&h=400&fit=crop&crop=center",
      tags: ["Django", "PostgreSQL", "Redis", "Elasticsearch"],
      category: "E-commerce",
      liveUrl: "#",
      githubUrl: "#",
      client: "Industrial Connect",
      duration: "8 mesi",
      features: ["Sistema rating", "Pagamenti escrow", "Chat integrata", "Analytics avanzati"]
    }
  ];

  const filteredProjects = selectedCategory === 'All' 
    ? projects 
    : projects.filter(project => project.category === selectedCategory);

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-background via-background-secondary to-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-text-primary mb-6">
              I Nostri{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent">
                Lavori
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
              Esplora il nostro portfolio di progetti realizzati. Ogni progetto racconta 
              una storia di successo e innovazione digitale.
            </p>
          </div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-12 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <div className="flex items-center gap-2 text-text-secondary">
              <Filter className="w-5 h-5" />
              <span className="font-medium">Filtra per categoria:</span>
            </div>
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="transition-all duration-200"
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="pb-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <AnimatePresence mode="wait">
            <motion.div
              key={selectedCategory}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              {filteredProjects.map((project, index) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card hover className="h-full overflow-hidden group cursor-pointer">
                    {/* Project Image */}
                    <div className="relative h-48 mb-6 overflow-hidden rounded-lg">
                      <Image
                        src={project.image}
                        alt={project.title}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-110"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      
                      {/* Project Links */}
                      <div className="absolute top-4 right-4 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <a
                          href={project.liveUrl}
                          className="p-2 bg-white/90 rounded-lg hover:bg-white transition-colors duration-200"
                          aria-label="View live project"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <ExternalLink className="w-4 h-4 text-gray-800" />
                        </a>
                        <a
                          href={project.githubUrl}
                          className="p-2 bg-white/90 rounded-lg hover:bg-white transition-colors duration-200"
                          aria-label="View source code"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Github className="w-4 h-4 text-gray-800" />
                        </a>
                      </div>
                    </div>

                    {/* Project Content */}
                    <div 
                      className="space-y-4"
                      onClick={() => setSelectedProject(project)}
                    >
                      <div className="flex items-center justify-between">
                        <span className="px-3 py-1 bg-primary/10 text-primary text-sm font-medium rounded-full">
                          {project.category}
                        </span>
                      </div>
                      
                      <h3 className="text-xl font-semibold text-text-primary group-hover:text-primary transition-colors duration-200">
                        {project.title}
                      </h3>
                      
                      <p className="text-text-secondary leading-relaxed">
                        {project.description}
                      </p>
                      
                      {/* Tags */}
                      <div className="flex flex-wrap gap-2">
                        {project.tags.map((tag) => (
                          <span
                            key={tag}
                            className="px-2 py-1 bg-background-secondary text-text-secondary text-xs rounded-md"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </AnimatePresence>
        </div>
      </section>

      {/* Project Detail Modal */}
      <Modal
        isOpen={!!selectedProject}
        onClose={() => setSelectedProject(null)}
        title={selectedProject?.title}
        size="xl"
      >
        {selectedProject && (
          <div className="space-y-6">
            <div className="relative h-64 rounded-lg overflow-hidden">
              <Image
                src={selectedProject.image}
                alt={selectedProject.title}
                fill
                className="object-cover"
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2 space-y-4">
                <p className="text-text-secondary leading-relaxed">
                  {selectedProject.fullDescription}
                </p>
                
                <div>
                  <h4 className="font-semibold text-text-primary mb-2">Caratteristiche Principali:</h4>
                  <ul className="space-y-1">
                    {selectedProject.features.map((feature) => (
                      <li key={feature} className="flex items-center text-sm text-text-secondary">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full mr-3"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-text-primary mb-2">Dettagli Progetto</h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-text-secondary">Cliente:</span>
                      <span className="ml-2 text-text-primary">{selectedProject.client}</span>
                    </div>
                    <div>
                      <span className="text-text-secondary">Durata:</span>
                      <span className="ml-2 text-text-primary">{selectedProject.duration}</span>
                    </div>
                    <div>
                      <span className="text-text-secondary">Categoria:</span>
                      <span className="ml-2 text-primary">{selectedProject.category}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-text-primary mb-2">Tecnologie</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedProject.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-md"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <Button size="sm" className="flex-1">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Live Demo
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Github className="w-4 h-4 mr-2" />
                    Codice
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default PortfolioPage;
