'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { ExternalLink, Github, ArrowRight } from 'lucide-react';

const FeaturedPortfolio = () => {
  const featuredProjects = [
    {
      id: 1,
      title: "E-commerce Fashion Store",
      description: "Piattaforma e-commerce completa per un brand di moda con sistema di gestione inventario, pagamenti integrati e dashboard analytics.",
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop&crop=center",
      tags: ["E-commerce", "React", "Node.js", "Stripe"],
      liveUrl: "#",
      githubUrl: "#",
      category: "E-commerce"
    },
    {
      id: 2,
      title: "Sistema Gestionale Ristorante",
      description: "Applicazione web per la gestione completa di un ristorante: ordini, menu, prenotazioni, inventario e reportistica avanzata.",
      image: "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=600&h=400&fit=crop&crop=center",
      tags: ["Management System", "Vue.js", "Laravel", "MySQL"],
      liveUrl: "#",
      githubUrl: "#",
      category: "Management System"
    },
    {
      id: 3,
      title: "Portfolio Architetto",
      description: "Sito web portfolio per studio di architettura con galleria interattiva, sistema CMS personalizzato e ottimizzazione SEO.",
      image: "https://images.unsplash.com/photo-1503387762-592deb58ef4e?w=600&h=400&fit=crop&crop=center",
      tags: ["Website", "Next.js", "Sanity CMS", "SEO"],
      liveUrl: "#",
      githubUrl: "#",
      category: "Website"
    }
  ];

  return (
    <section className="py-20 bg-background-secondary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary mb-6">
            I Nostri{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent">
              Progetti in Evidenza
            </span>
          </h2>
          <p className="text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
            Scopri alcuni dei progetti che abbiamo realizzato per i nostri clienti. 
            Ogni progetto è unico e sviluppato su misura per soddisfare specifiche esigenze di business.
          </p>
        </motion.div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {featuredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
            >
              <Card hover className="h-full overflow-hidden group">
                {/* Project Image */}
                <div className="relative h-48 mb-6 overflow-hidden rounded-lg">
                  <Image
                    src={project.image}
                    alt={project.title}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  
                  {/* Project Links */}
                  <div className="absolute top-4 right-4 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <a
                      href={project.liveUrl}
                      className="p-2 bg-white/90 rounded-lg hover:bg-white transition-colors duration-200"
                      aria-label="View live project"
                    >
                      <ExternalLink className="w-4 h-4 text-gray-800" />
                    </a>
                    <a
                      href={project.githubUrl}
                      className="p-2 bg-white/90 rounded-lg hover:bg-white transition-colors duration-200"
                      aria-label="View source code"
                    >
                      <Github className="w-4 h-4 text-gray-800" />
                    </a>
                  </div>
                </div>

                {/* Project Content */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="px-3 py-1 bg-primary/10 text-primary text-sm font-medium rounded-full">
                      {project.category}
                    </span>
                  </div>
                  
                  <h3 className="text-xl font-semibold text-text-primary group-hover:text-primary transition-colors duration-200">
                    {project.title}
                  </h3>
                  
                  <p className="text-text-secondary leading-relaxed">
                    {project.description}
                  </p>
                  
                  {/* Tags */}
                  <div className="flex flex-wrap gap-2">
                    {project.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-background-secondary text-text-secondary text-xs rounded-md"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* View All Projects Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Link href="/i-nostri-lavori">
            <Button size="lg" className="group">
              Vedi tutti i progetti
              <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default FeaturedPortfolio;
