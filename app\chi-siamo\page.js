import { motion } from 'framer-motion';
import Image from 'next/image';
import { Users, Target, Lightbulb, Heart } from 'lucide-react';
import Card from '../components/ui/Card';

export const metadata = {
  title: "Chi Siamo - ProgrammArti | La Nostra Storia e il Nostro Team",
  description: "Scopri la storia di ProgrammArti, la nostra missione e il team di professionisti che trasforma le idee digitali in realtà. Conosci i nostri sviluppatori e social media manager.",
  keywords: "chi siamo, team, storia azienda, programmatori, social media manager, agenzia web Roma",
};

const AboutPage = () => {
  const values = [
    {
      icon: Target,
      title: "Orientati ai Risultati",
      description: "Ogni progetto è sviluppato con l'obiettivo di generare valore concreto per il business dei nostri clienti."
    },
    {
      icon: Lightbulb,
      title: "Innovazione Continua",
      description: "Utilizziamo sempre le tecnologie più avanzate e le metodologie più innovative per rimanere all'avanguardia."
    },
    {
      icon: Users,
      title: "Collaborazione",
      description: "Lavoriamo a stretto contatto con i nostri clienti, considerandoci un'estensione del loro team."
    },
    {
      icon: Heart,
      title: "Passione",
      description: "Amiamo quello che facciamo e mettiamo passione in ogni singolo progetto che realizziamo."
    }
  ];

  const team = [
    {
      name: "Marco Rossi",
      role: "Lead Developer & Co-Founder",
      description: "Esperto in sviluppo full-stack con oltre 8 anni di esperienza. Specializzato in React, Node.js, Python e architetture cloud. Appassionato di tecnologie emergenti e best practices di sviluppo.",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
      skills: ["React", "Node.js", "Python", "AWS", "Docker", "MongoDB"],
      linkedin: "#",
      github: "#"
    },
    {
      name: "Sofia Bianchi",
      role: "Social Media Manager & Co-Founder",
      description: "Esperta in marketing digitale e gestione social media con 6 anni di esperienza. Specializzata in strategie di content marketing, advertising e analisi delle performance.",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face",
      skills: ["Social Media Strategy", "Content Marketing", "Google Ads", "Facebook Ads", "Analytics", "SEO"],
      linkedin: "#",
      instagram: "#"
    }
  ];

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-background via-background-secondary to-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-text-primary mb-6">
              Chi{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent">
                Siamo
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
              Siamo ProgrammArti, un team di professionisti appassionati che trasforma 
              le idee digitali in soluzioni innovative e di successo.
            </p>
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-text-primary mb-6">
                La Nostra Storia
              </h2>
              <div className="space-y-6 text-text-secondary leading-relaxed">
                <p>
                  ProgrammArti nasce nel 2019 dalla passione condivisa di Marco e Sofia per 
                  il mondo digitale e l'innovazione tecnologica. Quello che è iniziato come 
                  un progetto tra amici si è rapidamente trasformato in una realtà consolidata 
                  nel panorama delle agenzie web italiane.
                </p>
                <p>
                  La nostra missione è sempre stata chiara: aiutare le aziende e i professionisti 
                  a sfruttare al meglio le opportunità offerte dal digitale, creando soluzioni 
                  su misura che generano valore concreto per il business.
                </p>
                <p>
                  Negli anni abbiamo sviluppato competenze trasversali che spaziano dallo sviluppo 
                  web al marketing digitale, dai sistemi gestionali alle innovative soluzioni NFC, 
                  sempre mantenendo al centro la qualità e la soddisfazione del cliente.
                </p>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-square rounded-2xl overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=600&h=600&fit=crop&crop=center"
                  alt="Team ProgrammArti al lavoro"
                  width={600}
                  height={600}
                  className="object-cover w-full h-full"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-20 bg-background-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-text-primary mb-6">
              I Nostri Valori
            </h2>
            <p className="text-xl text-text-secondary max-w-3xl mx-auto">
              Questi sono i principi che guidano il nostro lavoro quotidiano e 
              che ci permettono di creare valore per i nostri clienti.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => {
              const Icon = value.icon;
              return (
                <Card key={value.title} hover className="text-center">
                  <div className="p-3 bg-primary/10 rounded-lg w-fit mx-auto mb-4">
                    <Icon className="w-8 h-8 text-primary" />
                  </div>
                  <h3 className="text-lg font-semibold text-text-primary mb-3">
                    {value.title}
                  </h3>
                  <p className="text-text-secondary text-sm leading-relaxed">
                    {value.description}
                  </p>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-text-primary mb-6">
              Il Nostro Team
            </h2>
            <p className="text-xl text-text-secondary max-w-3xl mx-auto">
              Conosci le persone che stanno dietro a ProgrammArti e che rendono 
              possibile la realizzazione dei tuoi progetti digitali.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {team.map((member, index) => (
              <Card key={member.name} hover className="overflow-hidden">
                <div className="flex flex-col md:flex-row gap-6">
                  <div className="flex-shrink-0">
                    <div className="w-32 h-32 rounded-full overflow-hidden mx-auto md:mx-0">
                      <Image
                        src={member.image}
                        alt={member.name}
                        width={128}
                        height={128}
                        className="object-cover w-full h-full"
                      />
                    </div>
                  </div>
                  <div className="flex-grow text-center md:text-left">
                    <h3 className="text-xl font-semibold text-text-primary mb-2">
                      {member.name}
                    </h3>
                    <p className="text-primary font-medium mb-4">
                      {member.role}
                    </p>
                    <p className="text-text-secondary text-sm leading-relaxed mb-4">
                      {member.description}
                    </p>
                    <div className="flex flex-wrap gap-2 justify-center md:justify-start">
                      {member.skills.map((skill) => (
                        <span
                          key={skill}
                          className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-md"
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
