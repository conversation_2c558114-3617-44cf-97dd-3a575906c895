'use client';

import { motion } from 'framer-motion';
import { 
  Code, 
  ShoppingCart, 
  Database, 
  TrendingUp, 
  Smartphone, 
  Search,
  ArrowRight 
} from 'lucide-react';
import Card from '../ui/Card';
import Button from '../ui/Button';

const Services = () => {
  const services = [
    {
      icon: Code,
      title: "Sviluppo Siti Web",
      description: "Creiamo siti web moderni, responsive e ottimizzati per i motori di ricerca. Utilizziamo le tecnologie più avanzate per garantire performance eccellenti.",
      features: ["Design Responsive", "SEO Ottimizzato", "Performance Elevate", "Sicurezza Avanzata"],
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: ShoppingCart,
      title: "E-commerce",
      description: "Sviluppiamo piattaforme e-commerce complete con sistemi di pagamento integrati, gestione inventario e analytics avanzati.",
      features: ["Pagamenti Sicuri", "Gestione Inventario", "Analytics Avanzati", "Mobile Optimized"],
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: Database,
      title: "Sistemi Gestionali",
      description: "Realizziamo software gestionali personalizzati per ottimizzare i processi aziendali e migliorare l'efficienza operativa.",
      features: ["Personalizzazione", "Integrazione API", "Reportistica", "Cloud Ready"],
      color: "from-purple-500 to-violet-500"
    },
    {
      icon: TrendingUp,
      title: "Web Marketing",
      description: "Strategie di marketing digitale per aumentare la visibilità online, generare lead qualificati e incrementare le conversioni.",
      features: ["SEO/SEM", "Social Media", "Email Marketing", "Analytics"],
      color: "from-orange-500 to-red-500"
    },
    {
      icon: Smartphone,
      title: "Servizi NFC",
      description: "Soluzioni innovative con tecnologia NFC per connettere il mondo fisico a quello digitale. Ideale per professionisti e aziende.",
      features: ["Portachiavi NFC", "Biglietti da Visita", "Menu Digitali", "Profili Social"],
      color: "from-pink-500 to-rose-500"
    },
    {
      icon: Search,
      title: "SEO & Analytics",
      description: "Ottimizzazione per motori di ricerca e analisi approfondite per migliorare la visibilità online e le performance del sito.",
      features: ["Audit SEO", "Keyword Research", "Google Analytics", "Reporting"],
      color: "from-indigo-500 to-blue-500"
    }
  ];

  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary mb-6">
            I Nostri{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent">
              Servizi
            </span>
          </h2>
          <p className="text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
            Offriamo soluzioni digitali complete per far crescere il tuo business online. 
            Dalla progettazione alla realizzazione, ti accompagniamo in ogni fase del processo.
          </p>
        </motion.div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {services.map((service, index) => {
            const Icon = service.icon;
            return (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card hover className="h-full group">
                  {/* Service Icon */}
                  <div className="relative mb-6">
                    <div className={`w-16 h-16 rounded-lg bg-gradient-to-r ${service.color} p-4 group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="w-full h-full text-white" />
                    </div>
                  </div>

                  {/* Service Content */}
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-text-primary group-hover:text-primary transition-colors duration-200">
                      {service.title}
                    </h3>
                    
                    <p className="text-text-secondary leading-relaxed">
                      {service.description}
                    </p>
                    
                    {/* Features List */}
                    <ul className="space-y-2">
                      {service.features.map((feature) => (
                        <li key={feature} className="flex items-center text-sm text-text-secondary">
                          <div className="w-1.5 h-1.5 bg-primary rounded-full mr-3"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-primary to-accent rounded-2xl p-8 md:p-12">
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
              Pronto a trasformare la tua idea in realtà?
            </h3>
            <p className="text-white/90 text-lg mb-8 max-w-2xl mx-auto">
              Contattaci per una consulenza gratuita e scopri come possiamo aiutarti 
              a raggiungere i tuoi obiettivi digitali.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="secondary" size="lg" className="bg-white text-primary hover:bg-gray-100">
                Richiedi preventivo gratuito
              </Button>
              <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-primary">
                Scopri i nostri progetti
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Services;
