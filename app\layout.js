import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "./contexts/ThemeContext";
import Header from "./components/Header";
import Footer from "./components/Footer";
import CookieConsent from "./components/CookieConsent";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "ProgrammArti - Agenzia Web Specializzata",
  description: "Agenzia web specializzata nella creazione di siti web, e-commerce, sistemi gestionali e campagne di web marketing. Trasformiamo le tue idee digitali in realtà.",
  keywords: "agenzia web, sviluppo siti web, e-commerce, web marketing, sistemi gestionali, NFC, Roma",
  authors: [{ name: "<PERSON>m<PERSON><PERSON><PERSON>" }],
  creator: "ProgrammArt<PERSON>",
  publisher: "ProgrammArti",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "it_IT",
    url: "https://www.programmarti.com",
    siteName: "ProgrammArti",
    title: "ProgrammArti - Agenzia Web Specializzata",
    description: "Agenzia web specializzata nella creazione di siti web, e-commerce, sistemi gestionali e campagne di web marketing.",
    images: [
      {
        url: "/images/logo/LOGO.jpg",
        width: 1200,
        height: 630,
        alt: "ProgrammArti Logo",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "ProgrammArti - Agenzia Web Specializzata",
    description: "Agenzia web specializzata nella creazione di siti web, e-commerce, sistemi gestionali e campagne di web marketing.",
    images: ["/images/logo/LOGO.jpg"],
  },
  icons: {
    icon: "/images/favicon/favicon.ico",
    shortcut: "/images/favicon/favicon-16x16.png",
    apple: "/images/favicon/apple-touch-icon.png",
  },
  manifest: "/images/favicon/site.webmanifest",
};

export default function RootLayout({ children }) {
  return (
    <html lang="it" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider>
          <div className="min-h-screen flex flex-col">
            <Header />
            <main className="flex-grow">
              {children}
            </main>
            <Footer />
            <CookieConsent />
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
