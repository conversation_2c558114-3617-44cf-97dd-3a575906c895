'use client';

import { motion } from 'framer-motion';
import { 
  Users, 
  Clock, 
  Award, 
  Headphones, 
  Zap, 
  Shield 
} from 'lucide-react';

const WhyChooseUs = () => {
  const reasons = [
    {
      icon: Users,
      title: "Team Esperto",
      description: "Un team di professionisti specializzati in diverse tecnologie e settori, pronti a trasformare le tue idee in soluzioni digitali innovative.",
      stat: "5+ anni",
      statLabel: "di esperienza"
    },
    {
      icon: Clock,
      title: "Consegna Puntuale",
      description: "Rispettiamo sempre le scadenze concordate, garantendo la consegna dei progetti nei tempi stabiliti senza compromettere la qualità.",
      stat: "100%",
      statLabel: "progetti consegnati in tempo"
    },
    {
      icon: Award,
      title: "Qualità Garantita",
      description: "Utilizziamo le migliori pratiche di sviluppo e testing per garantire soluzioni robuste, sicure e performanti.",
      stat: "50+",
      statLabel: "progetti completati"
    },
    {
      icon: Headphones,
      title: "Supporto Continuo",
      description: "Offriamo supporto tecnico e manutenzione continua per garantire che le tue soluzioni digitali funzionino sempre al meglio.",
      stat: "24/7",
      statLabel: "supporto disponibile"
    },
    {
      icon: Zap,
      title: "Tecnologie Avanzate",
      description: "Utilizziamo le tecnologie più moderne e innovative per creare soluzioni all'avanguardia che ti danno un vantaggio competitivo.",
      stat: "10+",
      statLabel: "tecnologie utilizzate"
    },
    {
      icon: Shield,
      title: "Sicurezza Prioritaria",
      description: "La sicurezza è al centro di tutto ciò che facciamo. Implementiamo le migliori pratiche di sicurezza per proteggere i tuoi dati.",
      stat: "100%",
      statLabel: "progetti sicuri"
    }
  ];

  return (
    <section className="py-20 bg-background-secondary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary mb-6">
            Perché Scegliere{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent">
              ProgrammArti
            </span>
          </h2>
          <p className="text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
            Siamo più di una semplice agenzia web. Siamo il tuo partner strategico 
            per il successo digitale, con un approccio personalizzato e orientato ai risultati.
          </p>
        </motion.div>

        {/* Reasons Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {reasons.map((reason, index) => {
            const Icon = reason.icon;
            return (
              <motion.div
                key={reason.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-background border border-border rounded-lg p-6 h-full hover:border-primary/30 hover:shadow-lg transition-all duration-300">
                  {/* Icon and Stat */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="p-3 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors duration-300">
                      <Icon className="w-8 h-8 text-primary" />
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-primary">
                        {reason.stat}
                      </div>
                      <div className="text-xs text-text-secondary">
                        {reason.statLabel}
                      </div>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="space-y-3">
                    <h3 className="text-xl font-semibold text-text-primary group-hover:text-primary transition-colors duration-200">
                      {reason.title}
                    </h3>
                    <p className="text-text-secondary leading-relaxed">
                      {reason.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-background border border-border rounded-2xl p-8 md:p-12">
            <h3 className="text-2xl md:text-3xl font-bold text-text-primary mb-4">
              Inizia il tuo progetto oggi stesso
            </h3>
            <p className="text-text-secondary text-lg mb-8 max-w-2xl mx-auto">
              Hai un'idea o un progetto in mente? Contattaci per una consulenza gratuita 
              e scopri come possiamo aiutarti a realizzarlo.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.a
                href="/contatti"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center justify-center px-8 py-4 bg-primary text-white font-medium rounded-lg hover:bg-primary-dark transition-colors duration-200"
              >
                Contattaci ora
              </motion.a>
              <motion.a
                href="/i-nostri-lavori"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center justify-center px-8 py-4 border border-border text-text-primary font-medium rounded-lg hover:bg-background-secondary transition-colors duration-200"
              >
                Vedi i nostri lavori
              </motion.a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
