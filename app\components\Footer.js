'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Mail, Phone, MapPin, Facebook, Instagram, Linkedin, Github } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { name: 'Home', href: '/' },
    { name: 'Chi Siamo', href: '/chi-siamo' },
    { name: 'I Nostri Lavori', href: '/i-nostri-lavori' },
    { name: 'Servizi NFC', href: '/servizi-nfc' },
    { name: 'Contat<PERSON>', href: '/contatti' },
  ];

  const services = [
    'Sviluppo Siti Web',
    'E-commerce',
    'Sistemi Gestionali',
    'Web Marketing',
    'Servizi NFC',
    'SEO & Analytics',
  ];

  const socialLinks = [
    { name: 'Facebook', icon: Facebook, href: '#' },
    { name: 'Instagram', icon: Instagram, href: '#' },
    { name: 'LinkedIn', icon: Linkedin, href: '#' },
    { name: 'GitHub', icon: Github, href: '#' },
  ];

  return (
    <footer className="bg-background-secondary border-t border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <Link href="/" className="flex items-center space-x-2">
              <Image
                src="/images/logo/LOGO.jpg"
                alt="ProgrammArti Logo"
                width={40}
                height={40}
                className="rounded-lg"
              />
              <span className="text-xl font-bold text-primary">
                ProgrammArti
              </span>
            </Link>
            <p className="text-text-secondary text-sm leading-relaxed">
              Agenzia web specializzata nella creazione di siti web, sistemi gestionali, 
              e-commerce e campagne di web marketing. Trasformiamo le tue idee in soluzioni digitali innovative.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => {
                const Icon = social.icon;
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    className="p-2 rounded-lg bg-background hover:bg-border transition-colors duration-200"
                    aria-label={social.name}
                  >
                    <Icon className="w-5 h-5 text-text-secondary hover:text-primary transition-colors duration-200" />
                  </a>
                );
              })}
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-text-primary">Link Rapidi</h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-text-secondary hover:text-primary transition-colors duration-200 text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-text-primary">I Nostri Servizi</h3>
            <ul className="space-y-2">
              {services.map((service) => (
                <li key={service}>
                  <span className="text-text-secondary text-sm">
                    {service}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-text-primary">Contatti</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-primary" />
                <a
                  href="mailto:<EMAIL>"
                  className="text-text-secondary hover:text-primary transition-colors duration-200 text-sm"
                >
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-primary" />
                <a
                  href="tel:+393123456789"
                  className="text-text-secondary hover:text-primary transition-colors duration-200 text-sm"
                >
                  +39 ************
                </a>
              </div>
              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-primary mt-0.5" />
                <span className="text-text-secondary text-sm">
                  Via Roma 123<br />
                  00100 Roma, Italia
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-12 pt-8 border-t border-border">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-text-secondary text-sm">
              © {currentYear} ProgrammArti. Tutti i diritti riservati.
            </p>
            <div className="flex space-x-6">
              <Link
                href="/privacy"
                className="text-text-secondary hover:text-primary transition-colors duration-200 text-sm"
              >
                Privacy Policy
              </Link>
              <Link
                href="/terms"
                className="text-text-secondary hover:text-primary transition-colors duration-200 text-sm"
              >
                Termini di Servizio
              </Link>
              <Link
                href="/cookies"
                className="text-text-secondary hover:text-primary transition-colors duration-200 text-sm"
              >
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
