'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>, Setting<PERSON>, <PERSON> } from 'lucide-react';
import Button from './ui/Button';
import Modal from './ui/Modal';

const CookieConsent = () => {
  const [showBanner, setShowBanner] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [preferences, setPreferences] = useState({
    necessary: true, // Always true, cannot be disabled
    analytics: false,
    marketing: false,
    functional: false,
  });

  useEffect(() => {
    // Check if user has already made a choice
    const consent = localStorage.getItem('cookie-consent');
    if (!consent) {
      // Show banner after a short delay
      const timer = setTimeout(() => {
        setShowBanner(true);
      }, 2000);
      return () => clearTimeout(timer);
    } else {
      // Load saved preferences
      try {
        const savedPreferences = JSON.parse(consent);
        setPreferences(savedPreferences);
        // Initialize analytics based on consent
        if (savedPreferences.analytics) {
          initializeAnalytics();
        }
      } catch (error) {
        console.error('Error parsing cookie consent:', error);
      }
    }
  }, []);

  const initializeAnalytics = () => {
    // Initialize Google Analytics or other analytics tools
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('consent', 'update', {
        analytics_storage: 'granted',
        ad_storage: preferences.marketing ? 'granted' : 'denied',
        functionality_storage: preferences.functional ? 'granted' : 'denied',
      });
    }
  };

  const handleAcceptAll = () => {
    const allAccepted = {
      necessary: true,
      analytics: true,
      marketing: true,
      functional: true,
    };
    setPreferences(allAccepted);
    localStorage.setItem('cookie-consent', JSON.stringify(allAccepted));
    setShowBanner(false);
    initializeAnalytics();
  };

  const handleAcceptNecessary = () => {
    const necessaryOnly = {
      necessary: true,
      analytics: false,
      marketing: false,
      functional: false,
    };
    setPreferences(necessaryOnly);
    localStorage.setItem('cookie-consent', JSON.stringify(necessaryOnly));
    setShowBanner(false);
  };

  const handleSavePreferences = () => {
    localStorage.setItem('cookie-consent', JSON.stringify(preferences));
    setShowBanner(false);
    setShowSettings(false);
    if (preferences.analytics) {
      initializeAnalytics();
    }
  };

  const handlePreferenceChange = (type) => {
    if (type === 'necessary') return; // Cannot disable necessary cookies
    setPreferences(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
  };

  const cookieTypes = [
    {
      id: 'necessary',
      title: 'Cookie Necessari',
      description: 'Questi cookie sono essenziali per il funzionamento del sito web e non possono essere disabilitati.',
      required: true
    },
    {
      id: 'analytics',
      title: 'Cookie Analitici',
      description: 'Ci aiutano a capire come i visitatori interagiscono con il sito raccogliendo informazioni in forma anonima.',
      required: false
    },
    {
      id: 'marketing',
      title: 'Cookie di Marketing',
      description: 'Utilizzati per tracciare i visitatori sui siti web per mostrare annunci pertinenti e coinvolgenti.',
      required: false
    },
    {
      id: 'functional',
      title: 'Cookie Funzionali',
      description: 'Permettono al sito di fornire funzionalità e personalizzazione migliorate.',
      required: false
    }
  ];

  return (
    <>
      {/* Cookie Banner */}
      <AnimatePresence>
        {showBanner && (
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed bottom-0 left-0 right-0 z-50 bg-background border-t border-border shadow-lg"
          >
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
              <div className="flex flex-col lg:flex-row items-start lg:items-center gap-4">
                <div className="flex items-start gap-3 flex-grow">
                  <Cookie className="w-6 h-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-text-primary mb-2">
                      Utilizziamo i cookie
                    </h3>
                    <p className="text-text-secondary text-sm leading-relaxed">
                      Utilizziamo cookie e tecnologie simili per migliorare la tua esperienza di navigazione, 
                      analizzare il traffico del sito e personalizzare i contenuti. Puoi scegliere quali 
                      cookie accettare.{' '}
                      <a href="/cookies" className="text-primary hover:underline">
                        Leggi la nostra Cookie Policy
                      </a>
                    </p>
                  </div>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowSettings(true)}
                    className="w-full sm:w-auto"
                  >
                    <Settings className="w-4 h-4 mr-2" />
                    Personalizza
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={handleAcceptNecessary}
                    className="w-full sm:w-auto"
                  >
                    Solo Necessari
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleAcceptAll}
                    className="w-full sm:w-auto"
                  >
                    Accetta Tutti
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Cookie Settings Modal */}
      <Modal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        title="Impostazioni Cookie"
        size="lg"
      >
        <div className="space-y-6">
          <p className="text-text-secondary">
            Puoi scegliere quali tipi di cookie accettare. Le tue preferenze verranno 
            salvate per le visite future.
          </p>
          
          <div className="space-y-4">
            {cookieTypes.map((cookieType) => (
              <div
                key={cookieType.id}
                className="flex items-start justify-between p-4 bg-background-secondary rounded-lg"
              >
                <div className="flex-grow pr-4">
                  <h4 className="font-semibold text-text-primary mb-2">
                    {cookieType.title}
                    {cookieType.required && (
                      <span className="ml-2 text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                        Obbligatorio
                      </span>
                    )}
                  </h4>
                  <p className="text-text-secondary text-sm">
                    {cookieType.description}
                  </p>
                </div>
                
                <div className="flex-shrink-0">
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences[cookieType.id]}
                      onChange={() => handlePreferenceChange(cookieType.id)}
                      disabled={cookieType.required}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                  </label>
                </div>
              </div>
            ))}
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowSettings(false)}
              className="flex-1"
            >
              Annulla
            </Button>
            <Button
              onClick={handleSavePreferences}
              className="flex-1"
            >
              Salva Preferenze
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default CookieConsent;
