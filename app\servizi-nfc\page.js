import { motion } from 'framer-motion';
import Image from 'next/image';
import { 
  Smartphone, 
  Users, 
  Building, 
  Utensils, 
  Briefcase, 
  Zap,
  CheckCircle,
  ArrowRight 
} from 'lucide-react';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';

export const metadata = {
  title: "Servizi NFC - ProgrammArti | Portachiavi e Gadget NFC Personalizzati",
  description: "Scopri i nostri servizi NFC: portachiavi, biglietti da visita e gadget personalizzati per professionisti, aziende e ristoranti. Connetti il mondo fisico a quello digitale.",
  keywords: "servizi NFC, portachiavi NFC, biglietti da visita NFC, gadget NFC, tecnologia NFC, marketing innovativo",
};

const NFCServicesPage = () => {
  const benefits = [
    {
      icon: Smartphone,
      title: "Connessione Istantanea",
      description: "Un semplice tocco per connettere smartphone a siti web, profili social e cataloghi digitali."
    },
    {
      icon: Zap,
      title: "Tecnologia Avanzata",
      description: "Utilizziamo chip NFC di ultima generazione per garantire compatibilità e affidabilità."
    },
    {
      icon: Users,
      title: "Esperienza Utente",
      description: "Migliora l'interazione con i tuoi clienti offrendo un'esperienza moderna e innovativa."
    },
    {
      icon: CheckCircle,
      title: "Facile da Usare",
      description: "Non serve installare app: funziona con tutti gli smartphone moderni con NFC attivo."
    }
  ];

  const useCases = [
    {
      icon: Briefcase,
      title: "Professionisti",
      description: "Biglietti da visita digitali, portfolio online, contatti diretti",
      examples: ["Avvocati", "Medici", "Consulenti", "Architetti"],
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: Building,
      title: "Aziende",
      description: "Brochure digitali, cataloghi prodotti, landing page personalizzate",
      examples: ["Immobiliari", "Servizi", "Manifattura", "Tecnologia"],
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: Utensils,
      title: "Ristoranti",
      description: "Menu digitali, recensioni, prenotazioni online, social media",
      examples: ["Ristoranti", "Bar", "Pizzerie", "Catering"],
      color: "from-orange-500 to-red-500"
    }
  ];

  const products = [
    {
      name: "Portachiavi NFC Premium",
      description: "Portachiavi elegante in metallo con chip NFC integrato. Personalizzabile con logo e colori aziendali.",
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center",
      features: ["Materiale premium", "Design personalizzabile", "Chip NFC di qualità", "Resistente all'acqua"],
      price: "Da €15"
    },
    {
      name: "Biglietto da Visita NFC",
      description: "Biglietto da visita moderno con tecnologia NFC integrata. Design elegante e professionale.",
      image: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop&crop=center",
      features: ["Design professionale", "Stampa di qualità", "Chip NFC invisibile", "Formato standard"],
      price: "Da €8"
    },
    {
      name: "Adesivi NFC Smart",
      description: "Adesivi NFC discreti e versatili, perfetti per qualsiasi superficie. Ideali per menu e brochure.",
      image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=300&fit=crop&crop=center",
      features: ["Adesivo forte", "Design discreto", "Facile applicazione", "Resistente"],
      price: "Da €3"
    }
  ];

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-background via-background-secondary to-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-text-primary mb-6">
              Servizi{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent">
                NFC
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
              Connetti il mondo fisico a quello digitale con le nostre soluzioni NFC innovative. 
              Perfette per professionisti, aziende e ristoranti.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-text-primary mb-6">
                Cosa sono i servizi NFC?
              </h2>
              <div className="space-y-4 text-text-secondary leading-relaxed">
                <p>
                  La tecnologia NFC (Near Field Communication) permette di trasferire informazioni 
                  tra dispositivi con un semplice tocco. I nostri gadget NFC personalizzati 
                  collegano istantaneamente gli smartphone dei tuoi clienti ai tuoi contenuti digitali.
                </p>
                <p>
                  Che si tratti di un sito web, profilo social, menu digitale o catalogo prodotti, 
                  con un semplice avvicinamento dello smartphone al gadget NFC, i tuoi clienti 
                  accederanno immediatamente alle tue informazioni.
                </p>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-video rounded-2xl overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop&crop=center"
                  alt="Tecnologia NFC in azione"
                  width={600}
                  height={400}
                  className="object-cover w-full h-full"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-text-primary mb-6">
              Perché Scegliere l'NFC?
            </h2>
            <p className="text-xl text-text-secondary max-w-3xl mx-auto">
              La tecnologia NFC offre vantaggi unici per migliorare l'interazione 
              con i tuoi clienti e modernizzare la tua presenza digitale.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon;
              return (
                <Card key={benefit.title} hover className="text-center">
                  <div className="p-3 bg-primary/10 rounded-lg w-fit mx-auto mb-4">
                    <Icon className="w-8 h-8 text-primary" />
                  </div>
                  <h3 className="text-lg font-semibold text-text-primary mb-3">
                    {benefit.title}
                  </h3>
                  <p className="text-text-secondary text-sm leading-relaxed">
                    {benefit.description}
                  </p>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-20 bg-background-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-text-primary mb-6">
              Perfetto per Ogni Settore
            </h2>
            <p className="text-xl text-text-secondary max-w-3xl mx-auto">
              Le nostre soluzioni NFC si adattano a qualsiasi tipo di business, 
              offrendo vantaggi concreti in diversi settori.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {useCases.map((useCase, index) => {
              const Icon = useCase.icon;
              return (
                <Card key={useCase.title} hover className="text-center">
                  <div className={`w-16 h-16 rounded-lg bg-gradient-to-r ${useCase.color} p-4 mx-auto mb-6`}>
                    <Icon className="w-full h-full text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-text-primary mb-4">
                    {useCase.title}
                  </h3>
                  <p className="text-text-secondary mb-6 leading-relaxed">
                    {useCase.description}
                  </p>
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-text-primary">Ideale per:</p>
                    <div className="flex flex-wrap gap-2 justify-center">
                      {useCase.examples.map((example) => (
                        <span
                          key={example}
                          className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-md"
                        >
                          {example}
                        </span>
                      ))}
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-text-primary mb-6">
              I Nostri Prodotti NFC
            </h2>
            <p className="text-xl text-text-secondary max-w-3xl mx-auto">
              Scegli tra la nostra gamma di prodotti NFC personalizzabili, 
              progettati per soddisfare ogni esigenza di business.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {products.map((product, index) => (
              <Card key={product.name} hover className="overflow-hidden">
                <div className="relative h-48 mb-6 overflow-hidden rounded-lg">
                  <Image
                    src={product.image}
                    alt={product.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-semibold text-text-primary">
                      {product.name}
                    </h3>
                    <span className="text-lg font-bold text-primary">
                      {product.price}
                    </span>
                  </div>
                  <p className="text-text-secondary leading-relaxed">
                    {product.description}
                  </p>
                  <ul className="space-y-2">
                    {product.features.map((feature) => (
                      <li key={feature} className="flex items-center text-sm text-text-secondary">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Button className="w-full">
                    Richiedi Preventivo
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary to-accent">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Pronto a Innovare il Tuo Business?
          </h2>
          <p className="text-white/90 text-xl mb-8 max-w-3xl mx-auto">
            Contattaci per scoprire come le nostre soluzioni NFC possono 
            trasformare il modo in cui interagisci con i tuoi clienti.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="secondary" size="lg" className="bg-white text-primary hover:bg-gray-100">
              Richiedi Consulenza Gratuita
            </Button>
            <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-primary">
              Vedi Esempi
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default NFCServicesPage;
