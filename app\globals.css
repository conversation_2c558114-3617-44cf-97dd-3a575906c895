@import "tailwindcss";

:root {
  /* Light mode colors */
  --background: #f8f9fa;
  --background-secondary: #e9ecef;
  --primary: #0084ff;
  --primary-dark: #005fa3;
  --accent: #00baff;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --border: #dee2e6;
  --button-background: #0084ff;
  --button-text: #ffffff;
  --input-background: #ffffff;
  --input-border: #ced4da;
  --input-text: #212529;
}

[data-theme="dark"] {
  /* Dark mode colors */
  --background: #121417;
  --background-secondary: #1e1f21;
  --primary: #00baff;
  --primary-dark: #0084ff;
  --accent: #4dcfff;
  --text-primary: #f8f9fa;
  --text-secondary: #adb5bd;
  --border: #2b2f33;
  --button-background: #0084ff;
  --button-text: #ffffff;
  --input-background: #1e1f21;
  --input-border: #495057;
  --input-text: #f8f9fa;
}

@theme inline {
  --color-background: var(--background);
  --color-background-secondary: var(--background-secondary);
  --color-primary: var(--primary);
  --color-primary-dark: var(--primary-dark);
  --color-accent: var(--accent);
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-border: var(--border);
  --color-button-background: var(--button-background);
  --color-button-text: var(--button-text);
  --color-input-background: var(--input-background);
  --color-input-border: var(--input-border);
  --color-input-text: var(--input-text);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--text-primary);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Wave animation keyframes */
@keyframes wave {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes wave-reverse {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(10px);
  }
}

.wave-animation {
  animation: wave 3s ease-in-out infinite;
}

.wave-animation-reverse {
  animation: wave-reverse 3s ease-in-out infinite;
  animation-delay: 1.5s;
}

/* Smooth transitions for interactive elements */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}
