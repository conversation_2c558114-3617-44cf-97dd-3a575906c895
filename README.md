# ProgrammArti - Portfolio Website

A comprehensive portfolio website for ProgrammArti, a web agency specializing in websites, e-commerce platforms, management systems, and web marketing campaigns.

## 🚀 Features

### ✅ **Complete Website Structure**
- **Home Page**: Hero section with wave animations, services overview, featured portfolio, and call-to-action sections
- **About Us** (`/chi-siamo`): Agency history, mission, and team member profiles
- **Portfolio** (`/i-nostri-lavori`): Interactive project gallery with filtering system
- **NFC Services** (`/servizi-nfc`): Comprehensive NFC services and products showcase
- **Contact** (`/contatti`): Contact form with validation and company information

### 🎨 **Design & UX**
- **Dark/Light Mode**: Complete theme system with smooth transitions
- **Responsive Design**: Mobile-first approach optimized for all devices
- **Modern Animations**: Framer Motion animations and wave effects
- **Interactive Elements**: Hover effects, smooth transitions, and visual feedback

### 🔧 **Technical Features**
- **Next.js 15**: Latest version with App Router
- **Tailwind CSS v4**: Modern styling with custom color scheme
- **TypeScript Ready**: Structured for easy TypeScript migration
- **SEO Optimized**: Unique meta tags, Open Graph, and Twitter cards
- **Performance**: Optimized images, lazy loading, and efficient components

### 🍪 **GDPR Compliance**
- **Cookie Consent**: Granular cookie preferences with smooth animations
- **Privacy Policy**: Complete privacy and cookie policy pages
- **Google Consent Mode v2**: Ready for analytics integration

### 📱 **Components**
- **Reusable UI Components**: Button, Card, Modal, and Form components
- **Theme Context**: Persistent dark/light mode preferences
- **Responsive Navigation**: Mobile-friendly header with theme toggle
- **Contact Form**: React Hook Form with validation

## 🛠 **Tech Stack**

- **Framework**: Next.js 15.3.3
- **Styling**: Tailwind CSS v4
- **Animations**: Framer Motion
- **Forms**: React Hook Form
- **Icons**: Lucide React
- **UI Components**: Headless UI
- **Image Optimization**: Next.js Image component

## 🚀 **Getting Started**

1. **Install dependencies**:
```bash
npm install
```

2. **Run the development server**:
```bash
npm run dev
```

3. **Open your browser**:
Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 **Project Structure**

```
app/
├── components/           # Reusable components
│   ├── ui/              # UI components (Button, Card, Modal)
│   ├── sections/        # Page sections (Hero, Services, etc.)
│   ├── Header.js        # Navigation header
│   ├── Footer.js        # Site footer
│   └── CookieConsent.js # GDPR cookie banner
├── contexts/            # React contexts
│   └── ThemeContext.js  # Dark/light mode context
├── chi-siamo/          # About us page
├── i-nostri-lavori/    # Portfolio page
├── servizi-nfc/        # NFC services page
├── contatti/           # Contact page
├── privacy/            # Privacy policy
├── cookies/            # Cookie policy
├── globals.css         # Global styles
└── layout.js           # Root layout
```

## 🎨 **Color Scheme**

The website uses a carefully crafted color scheme for both light and dark modes:

**Light Mode**:
- Background: `#f8f9fa`
- Primary: `#0084ff`
- Accent: `#00baff`

**Dark Mode**:
- Background: `#121417`
- Primary: `#00baff`
- Accent: `#4dcfff`

## 📝 **Customization**

### **Update Company Information**
1. Edit contact details in `app/contatti/ContactClient.js`
2. Update social media links in `app/components/Footer.js`
3. Replace placeholder images with actual company photos

### **Add Real Projects**
1. Update project data in `app/i-nostri-lavori/PortfolioClient.js`
2. Replace Unsplash images with actual project screenshots
3. Add real client information and project details

### **Configure Analytics**
1. Add Google Analytics tracking ID
2. Update cookie consent settings in `app/components/CookieConsent.js`
3. Implement Google Consent Mode v2

## 🚀 **Deployment**

The website is ready for deployment on platforms like:
- **Vercel** (recommended for Next.js)
- **Netlify**
- **AWS Amplify**
- **Traditional hosting** with Node.js support

## 📄 **License**

This project is created for ProgrammArti. All rights reserved.

## 🤝 **Support**

For questions or support, contact: <EMAIL>
